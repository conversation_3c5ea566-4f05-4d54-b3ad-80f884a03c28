import { Link } from 'react-router-dom';

const AboutPage = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      bio: "Indie game enthusiast with 10+ years in game development and publishing.",
      image: "https://randomuser.me/api/portraits/women/32.jpg"
    },
    {
      name: "<PERSON>",
      role: "Lead Developer",
      bio: "Full-stack developer passionate about creating platforms that help indie creators thrive.",
      image: "https://randomuser.me/api/portraits/men/45.jpg"
    },
    {
      name: "<PERSON><PERSON>",
      role: "Community Manager",
      bio: "Former indie game developer who loves building bridges between players and creators.",
      image: "https://randomuser.me/api/portraits/women/68.jpg"
    },
    {
      name: "<PERSON>",
      role: "Content Curator",
      bio: "Game journalist with a knack for discovering hidden gems in the indie game world.",
      image: "https://randomuser.me/api/portraits/men/22.jpg"
    }
  ];

  const features = [
    {
      title: "Discover Unique Games",
      description: "Find carefully curated indie games from talented developers around the world.",
      icon: "🔍"
    },
    {
      title: "Connect with Developers",
      description: "Follow your favorite indie game creators and stay updated on their latest projects.",
      icon: "🤝"
    },
    {
      title: "Community Reviews",
      description: "Read authentic reviews from fellow indie game enthusiasts and share your own experiences.",
      icon: "✍️"
    },
    {
      title: "Early Access & Betas",
      description: "Get exclusive access to upcoming indie titles and help shape their development.",
      icon: "🚀"
    }
  ];

  return (
    <div className="text-white bg-gray-900">
      {/* Hero Section */}
      <section 
        className="relative h-96 md:h-[400px] bg-cover bg-center flex items-center justify-center text-center"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1552820728-8b83bb6b773f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80')"
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/90"></div>
        <div className="relative z-10 max-w-4xl px-5">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-5 bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">
            About IndieRepo
          </h1>
          <p className="text-lg md:text-xl text-gray-300">
            Your gateway to the magical world of indie games
          </p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="bg-gray-800">
        <div className="max-w-6xl mx-auto py-16 px-5">
          <h2 className="text-center text-3xl md:text-4xl font-bold mb-10 text-white relative">
            Our Mission
            <span className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-red-500 to-orange-500 rounded"></span>
          </h2>
          <div className="flex flex-wrap gap-10">
            <div className="flex-1 min-w-[300px]">
              <p className="text-lg leading-relaxed text-gray-300 mb-5">
                At IndieRepo, we believe that the most innovative and heartfelt gaming experiences often come from independent developers who pour their passion into their craft. Our mission is to create a vibrant platform where these creators can showcase their work and connect with players who appreciate unique gaming experiences.
              </p>
              <p className="text-lg leading-relaxed text-gray-300">
                Founded in 2023, IndieRepo aims to address the discovery challenges faced by both indie developers and players in an increasingly crowded gaming market. We&apos;re committed to highlighting quality indie games that might otherwise fly under the radar, supporting the developers who create them, and fostering a community of players who value creativity, innovation, and authenticity.
              </p>
            </div>
            <div className="flex-none w-full md:w-80 flex flex-col md:flex-row md:flex-col gap-5">
              <div className="bg-gray-900 p-8 rounded-xl text-center shadow-xl flex-1 md:flex-none">
                <h3 className="text-3xl md:text-4xl font-bold mb-1 bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">
                  1,200+
                </h3>
                <p className="text-gray-300">Indie Games</p>
              </div>
              <div className="bg-gray-900 p-8 rounded-xl text-center shadow-xl flex-1 md:flex-none">
                <h3 className="text-3xl md:text-4xl font-bold mb-1 bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">
                  700+
                </h3>
                <p className="text-gray-300">Developers</p>
              </div>
              <div className="bg-gray-900 p-8 rounded-xl text-center shadow-xl flex-1 md:flex-none">
                <h3 className="text-3xl md:text-4xl font-bold mb-1 bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">
                  50K+
                </h3>
                <p className="text-gray-300">Community Members</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="bg-gray-900">
        <div className="max-w-6xl mx-auto py-16 px-5">
          <h2 className="text-center text-3xl md:text-4xl font-bold mb-10 text-white relative">
            Meet Our Team
            <span className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-red-500 to-orange-500 rounded"></span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <div 
                key={index} 
                className="bg-gray-800 rounded-xl overflow-hidden shadow-xl transition-transform duration-300 hover:-translate-y-3"
              >
                <div className="h-48 overflow-hidden">
                  <img 
                    src={member.image} 
                    alt={member.name} 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-5">
                  <h3 className="text-xl font-semibold mb-1 text-white">{member.name}</h3>
                  <h4 className="text-red-500 text-base mb-4">{member.role}</h4>
                  <p className="text-gray-300 leading-relaxed">{member.bio}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-gray-800">
        <div className="max-w-6xl mx-auto py-16 px-5">
          <h2 className="text-center text-3xl md:text-4xl font-bold mb-10 text-white relative">
            What We Offer
            <span className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-red-500 to-orange-500 rounded"></span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index} 
                className="bg-gray-900 p-8 rounded-xl text-center shadow-xl transition-transform duration-300 hover:-translate-y-3 flex flex-col items-center"
              >
                <div className="text-5xl mb-5">{feature.icon}</div>
                <h3 className="text-xl font-semibold mb-4 text-white">{feature.title}</h3>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section 
        className="bg-cover bg-center text-center"
        style={{
          backgroundImage: "linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url('https://images.unsplash.com/photo-1550745165-9bc0b252726f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80')"
        }}
      >
        <div className="max-w-6xl mx-auto py-16 px-5">
          <h2 className="text-center text-3xl md:text-4xl font-bold mb-10 text-white relative">
            Join Our Community
            <span className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-gradient-to-r from-red-500 to-orange-500 rounded"></span>
          </h2>
          <p className="text-lg md:text-xl text-gray-300 mb-8">
            Become part of the IndieRepo family and start discovering amazing indie games today.
          </p>
          <div className="flex flex-col md:flex-row justify-center items-center gap-5">
            <p className="text-center text-gray-300 text-lg max-w-2xl">
              Use the login button in the header to join our community and start discovering amazing indie games!
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
