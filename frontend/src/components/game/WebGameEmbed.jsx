import { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { FaThumbsUp, FaThumbsDown, FaExclamationCircle, FaPlayCircle, FaExpand, FaCompress, FaHeart, FaGamepad } from 'react-icons/fa';
import { useLanguage } from '../../context/LanguageContext';
import LoadingSpinner from '../LoadingSpinner';
import { BASE_URL } from '../../config/env.js';
import { trackGamePlay } from '../../services/gameService';

/**
 * Component for embedding web playable games with fullscreen and interaction controls
 */
const WebGameEmbed = ({ game, onError, gameInteractions, interactionLoading, onLikeGame, onDislikeGame, onToggleFavorite, user }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [debugInfo, setDebugInfo] = useState({});
  const [gameStarted, setGameStarted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showExitMessage, setShowExitMessage] = useState(false);
  const { t } = useLanguage();
  
  // Add CSS to hide scrollbars globally for this component
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      #game-container-${game.id} iframe::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
      }
      #game-container-${game.id}::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
      }
      #game-container-${game.id} iframe {
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        overflow: hidden !important;
      }
      #game-container-${game.id} {
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        overflow: hidden !important;
      }
      /* Hide scrollbars in iframe content */
      #game-container-${game.id} iframe html {
        overflow: hidden !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
      }
      #game-container-${game.id} iframe body {
        overflow: hidden !important;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
      }
      #game-container-${game.id} iframe html::-webkit-scrollbar,
      #game-container-${game.id} iframe body::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, [game.id]);
  
  // Memoize the embed URL calculation to prevent infinite re-renders
  const { embedUrl, debugData } = useMemo(() => {
    let embedUrl = null;
    let debugData = {};
    
    // Priority 1: If there's a direct web_game_url (from S3 extracted files), use it
    if (game.web_game_url) {
      debugData.source = 'web_game_url';
      debugData.url = game.web_game_url;
      return { embedUrl: game.web_game_url, debugData };
    }
    
    // Priority 2: Special case for legacy Unity game (ID 7) - keep for backward compatibility
    if (game.id === 7) {
      const baseUrl = BASE_URL;
      // Use the relay.html page instead of index.html
      const unityGamePath = '/uploads/games/7/files/2dplatformerweb/relay.html';
      embedUrl = `${baseUrl}${unityGamePath}`;
      debugData.source = 'legacy_unity_game';
      debugData.url = embedUrl;
      return { embedUrl, debugData };
    }
    
    // Priority 3: Fallback to legacy embedded web files (for backward compatibility)
    // Note: New uploads use S3 extraction and set web_game_url, so this is mainly for old games
    if (game.has_embedded_version && game.files) {
      const webPlayableFile = game.files.find(file =>
        file.is_web_playable && !file.requires_purchase
      );
      if (webPlayableFile) {
        // Use the centralized base URL configuration
        const baseUrl = BASE_URL;
        debugData.baseUrl = baseUrl;
        // Get the file path and ensure proper formatting
        let filePath = webPlayableFile.filePath || '';
        debugData.originalFilePath = filePath;
        // Make sure the file path starts with a slash
        if (!filePath.startsWith('/')) {
          filePath = `/${filePath}`;
        }
        debugData.normalizedFilePath = filePath;
        // For 2dplatformerweb special case or Unity web builds
        if (filePath.includes('2dplatformerweb') || webPlayableFile.file_name?.includes('2dplatformerweb')) {
          // For Unity builds, we need to point directly to the entry point HTML file
          const entryPoint = webPlayableFile.web_entry_point || 'index.html';
          debugData.entryPoint = entryPoint;
          // If it's a ZIP file, we need to construct the path to the extracted content
          let webPath = filePath;
          if (webPlayableFile.file_name?.endsWith('.zip')) {
            // For ZIP files, assume they're extracted to a folder with the same name (without .zip)
            const fileName = webPlayableFile.file_name.replace('.zip', '');
            webPath = `/uploads/games/${game.id}/files/${fileName}`;
          }
          // Construct the final URL: baseUrl + webPath + entry point
          // Make sure we don't double-up on slashes
          if (webPath.endsWith('/')) {
            embedUrl = `${baseUrl}${webPath}${entryPoint}`;
          } else {
            embedUrl = `${baseUrl}${webPath}/${entryPoint}`;
          }
          debugData.constructedUrl = embedUrl;
        } else {
          // For other web files, construct the path to the directory
          const pathParts = filePath.split('/');
          // Check if the last part is a file or a directory
          // If it looks like a file (has an extension), remove it to get the directory
          const lastPart = pathParts[pathParts.length - 1];
          if (lastPart.includes('.') && !lastPart.endsWith('/')) {
            pathParts.pop(); // Remove the file part
          }
          let basePath = `${baseUrl}${pathParts.join('/')}`;
          if (!basePath.endsWith('/')) {
            basePath += '/';
          }
          debugData.basePath = basePath;
          // Append the web entry point
          const entryPoint = webPlayableFile.web_entry_point || '';
          debugData.entryPoint = entryPoint;
          if (entryPoint) {
            embedUrl = `${basePath}${entryPoint}`;
            debugData.constructedUrl = embedUrl;
          }
        }
      }
    }
    return { embedUrl, debugData };
  }, [game.id, game.web_game_url, game.has_embedded_version, game.files]);
  
  // Update debug info when it changes
  useEffect(() => {
    setDebugInfo(debugData);
  }, [debugData]);
  
  const handleLoad = () => {
    setIsLoading(false);
    // Try to inject CSS into iframe to hide scrollbars
    try {
      const iframe = document.querySelector(`#game-container-${game.id} iframe`);
      if (iframe && iframe.contentDocument) {
        const iframeDoc = iframe.contentDocument;
        const style = iframeDoc.createElement('style');
        style.textContent = `
          html, body {
            overflow: hidden !important;
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
          }
          html::-webkit-scrollbar, body::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
          }
          * {
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
          }
          *::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
          }
        `;
        iframeDoc.head.appendChild(style);
      }
    } catch (error) {
      // Cross-origin restrictions might prevent this, but that's okay
      console.log('Could not inject CSS into iframe (likely cross-origin):', error.message);
    }
  };
  
  const handleError = () => {
    console.error("Failed to load game from URL:", embedUrl, debugInfo);
    setIsLoading(false);
    setError("Failed to load web game");
    if (onError) onError();
  };
  
  // Fullscreen functionality
  const enterFullscreen = async (element) => {
    try {
      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else if (element.mozRequestFullScreen) {
        await element.mozRequestFullScreen();
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen();
      }
    } catch (error) {
      console.error('Error entering fullscreen:', error);
    }
  };
  
  const exitFullscreen = async () => {
    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        await document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        await document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) {
        await document.msExitFullscreen();
      }
    } catch (error) {
      console.error('Error exiting fullscreen:', error);
    }
  };
  
  const toggleFullscreen = async () => {
    const gameContainer = document.getElementById(`game-container-${game.id}`);
    if (!gameContainer) return;
    if (isFullscreen) {
      await exitFullscreen();
    } else {
      await enterFullscreen(gameContainer);
    }
  };
  
  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
      if (isCurrentlyFullscreen && gameStarted) {
        // Show exit message for 5 seconds when entering fullscreen
        setShowExitMessage(true);
        setTimeout(() => setShowExitMessage(false), 5000);
      }
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, [gameStarted]);
  
  const handlePlayClick = async () => {
    setGameStarted(true);

    // Track game play
    try {
      if (user) {
        await trackGamePlay(game.id);
        console.log('Game play tracked successfully');
      }
    } catch (error) {
      console.error('Failed to track game play:', error);
      // Don't prevent game from starting if tracking fails
    }

    // Automatically enter fullscreen when play is clicked
    setTimeout(async () => {
      const gameContainer = document.getElementById(`game-container-${game.id}`);
      if (gameContainer) {
        await enterFullscreen(gameContainer);
      }
    }, 100); // Small delay to ensure the iframe is rendered
  };
  
  if (!embedUrl) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-gray-800 rounded-lg border border-gray-700">
        <FaExclamationCircle className="text-red-400 text-4xl mb-4" />
        <p className="text-gray-300 text-lg mb-2">Web game URL not available</p>
        <div className="text-gray-400 text-sm mb-4 text-center">
          <p>Web game URL: {game.web_game_url || 'Not set'}</p>
        </div>
        <details className="text-gray-500 text-xs">
          <summary className="cursor-pointer mb-2">Debug Information</summary>
          <pre className="font-mono bg-gray-900 p-2 rounded max-w-full overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </details>
      </div>
    );
  }
  
  return (
    <div className="relative w-full">
      <div
        id={`game-container-${game.id}`}
        className={`relative w-full bg-gray-900 rounded-lg border border-gray-700 ${
          isFullscreen
            ? 'h-screen'
            : 'h-[50vh] min-h-[400px] max-h-[600px] lg:h-[60vh] lg:min-h-[500px] lg:max-h-[700px]'
        }`}
        style={{
          overflow: 'hidden',
          scrollbarWidth: 'none', /* Firefox */
          msOverflowStyle: 'none'  /* Internet Explorer and Edge */
        }}
      >
        {!gameStarted && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900 z-20">
            <div className="text-center">
              <button
                onClick={handlePlayClick}
                className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-8 py-4 rounded-lg font-bold text-xl transition-all duration-200 transform hover:scale-105 shadow-lg flex items-center gap-3 mx-auto"
              >
                <FaPlayCircle className="text-2xl" />
                Play Game
              </button>
            </div>
          </div>
        )}
        {gameStarted && isLoading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-800 z-10">
            <LoadingSpinner size="md" color="secondary" showText={true} text="Loading web game..." />
          </div>
        )}
        {gameStarted && error && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-800 z-10 p-4">
            <FaExclamationCircle className="text-red-400 text-4xl mb-4" />
            <p className="text-red-400 text-lg mb-2">{error}</p>
            <p className="text-gray-500 text-sm mb-4">Attempted URL: {embedUrl}</p>
            <a
              href={embedUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
            >
              <FaGamepad />
              Play in new window
            </a>
          </div>
        )}
        {gameStarted && (
          <iframe
            src={embedUrl}
            className="w-full h-full border-0"
            style={{
              overflow: 'hidden',
              scrollbarWidth: 'none', /* Firefox */
              msOverflowStyle: 'none',  /* Internet Explorer and Edge */
              WebkitOverflowScrolling: 'touch', /* iOS smooth scrolling */
              position: 'relative',
              display: 'block'
            }}
            onLoad={handleLoad}
            onError={handleError}
            allow="fullscreen; autoplay; gamepad; keyboard; microphone; camera; midi; encrypted-media"
            title={`Play ${game.title}`}
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-pointer-lock allow-modals"
          ></iframe>
        )}
        {/* ESC Exit Message Overlay */}
        {showExitMessage && isFullscreen && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-black bg-opacity-80 text-white px-6 py-3 rounded-lg shadow-lg backdrop-blur-sm">
            <p className="text-lg font-semibold text-center">
              {t('game.fullscreen.exitMessage')}
            </p>
          </div>
        )}
        {/* Fullscreen Toggle Button - Keep this overlaid for easy access */}
        {gameStarted && !isLoading && !error && (
          <button
            onClick={toggleFullscreen}
            className="absolute top-4 right-4 z-40 bg-black bg-opacity-60 hover:bg-opacity-80 text-white p-2 rounded-md transition-all duration-200 backdrop-blur-sm"
            title={isFullscreen ? t('game.fullscreen.exitFullscreen') : t('game.fullscreen.enterFullscreen')}
          >
            {isFullscreen ? <FaCompress className="text-sm" /> : <FaExpand className="text-sm" />}
          </button>
        )}
      </div>
      {/* Game Interaction Buttons - Below the game */}
      {user && (
        <div className="bg-gray-900 border-t border-gray-700 px-6 py-3">
          <div className="flex items-center justify-center gap-4">
            <button
              onClick={onLikeGame}
              disabled={interactionLoading}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 ${
                gameInteractions.userLiked
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } ${interactionLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              title="Like this game"
            >
              <FaThumbsUp className="text-sm" />
              <span className="text-sm font-medium">{gameInteractions.likes}</span>
            </button>
            <button
              onClick={onDislikeGame}
              disabled={interactionLoading}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 ${
                gameInteractions.userDisliked
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } ${interactionLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              title="Dislike this game"
            >
              <FaThumbsDown className="text-sm" />
              <span className="text-sm font-medium">{gameInteractions.dislikes}</span>
            </button>
            <button
              onClick={onToggleFavorite}
              disabled={interactionLoading}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 ${
                gameInteractions.isFavorite
                  ? 'bg-pink-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              } ${interactionLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={gameInteractions.isFavorite ? "Remove from favorites" : "Add to favorites"}
            >
              <FaHeart className="text-sm" />
              <span className="text-sm font-medium">
                {gameInteractions.isFavorite ? "Favorited" : "Favorite"}
              </span>
            </button>
            {/* Fullscreen Controls */}
            <div className="flex items-center gap-2 border-l border-gray-600 pl-4">
              <button
                onClick={toggleFullscreen}
                className="flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-200 bg-gray-700 text-gray-300 hover:bg-gray-600"
                title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
              >
                {isFullscreen ? (
                  <>
                    <FaCompress className="text-sm" />
                    <span className="text-sm font-medium">Exit Fullscreen</span>
                  </>
                ) : (
                  <>
                    <FaExpand className="text-sm" />
                    <span className="text-sm font-medium">Fullscreen</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

WebGameEmbed.propTypes = {
  game: PropTypes.shape({
    id: PropTypes.number,
    title: PropTypes.string,
    web_game_url: PropTypes.string,
    has_embedded_version: PropTypes.bool,
    files: PropTypes.array
  }).isRequired,
  onError: PropTypes.func,
  gameInteractions: PropTypes.object.isRequired,
  interactionLoading: PropTypes.bool.isRequired,
  onLikeGame: PropTypes.func.isRequired,
  onDislikeGame: PropTypes.func.isRequired,
  onToggleFavorite: PropTypes.func.isRequired,
  user: PropTypes.object
};

export default WebGameEmbed; 